#%%
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

def smooth(y, box_pts):
    """
    Smooths a 1D array using a moving average filter.

    Args:
        y (np.ndarray): The input 1D array to be smoothed.
        box_pts (int): The size of the moving average window.

    Returns:
        np.ndarray: The smoothed array.
    """
    box = np.ones(box_pts) / box_pts
    return np.convolve(y, box, mode='same')

def loadmat(filename):
    """
    Load .mat file using the appropriate function based on the file version.

    Args:
        filename (str): The path to the .mat file.

    Returns:
        dict: The loaded data.
    """
    from scipy.io import loadmat as loadmat_scipy
    from mat73 import loadmat as loadmat_mat73

    try:
        return loadmat_scipy(filename)
    except Exception as e:
        return loadmat_mat73(filename)

#%%
# --- Constants and Script Execution ---
# Define the base directory for your data
# IMPORTANT: Update this path to your actual data directory
datagen_dir = '/home/<USER>/code/Data_for_paper/'

# Define the monkeys and tasks to be analyzed
monkeys = ['monkeyF', 'monkeyN']
#tasks = ['lums', 'sacc']
tasks = ['lums']
task = tasks[0]


# Set the Signal-to-Noise Ratio (SNR) threshold
snr_threshold = 1.0

# Create a Path object for the base data directory
base_data_dir = Path(datagen_dir)


#%%

monkey = 'monkeyF'
monkey = 'monkeyN'
# Define channel ranges based on the monkey
if monkey == 'monkeyF':
    array_chns = np.arange(129, 193)
else:
    array_chns = np.arange(193, 257)

# Create a boolean mask for the channels of the current monkey
all_chns = np.zeros(512, dtype=bool)
all_chns[array_chns - 1] = True # Subtract 1 for 0-based indexing

# Construct file paths using pathlib
mat_filename = base_data_dir / monkey / f"ObjAtt_GAC2_lums_MUA_trials.mat"
norm_mua_filename = base_data_dir / monkey / f"ObjAtt_GAC2_lums_normMUA.mat"

# Load the .mat files
allmat_data = loadmat(mat_filename)
ALLMAT = allmat_data['ALLMAT']
tb = allmat_data['tb'].flatten()

norm_mua_data = loadmat(norm_mua_filename)
normMUA = norm_mua_data['normMUA']

cals_file = base_data_dir / monkey 
if monkey == 'monkeyN':
    cals_file /= 'cals_monkeyN_20250218_B1.mat'
else:
    cals_file /= 'cals_monkeyF_20250228_B1.mat'

cals = loadmat(cals_file)

obj_att_files = list((base_data_dir / monkey).glob('ObjAtt_GAC2_lums_*_B*.mat'))
obj_att_files.sort(reverse=False)

n_pre = int(.2 * 30000)
n_post = int(.5 * 30000)

dpi_l_trials = []
dpi_r_trials = []
for f in obj_att_files:
    data = loadmat(f)
    print(f'File: {f.name}')

    dpi_file = f.parent/ (f'TEMP_DPI_{f.name[-15:]}')
    print(f'DPI File: {dpi_file.name}')
    dpi = loadmat(dpi_file)

    eye_file = f.parent/ (f'TEMP_EYE_{f.name[-15:]}')
    print(f'Eye File: {eye_file.name}')
    eye = loadmat(eye_file)
    eye_trials = eye['Trials_corrected'].flatten()
    print(eye_trials.shape)

    dpi_l = dpi['dpi_l'][:,:2] @ cals['left_gains'] + cals['left_offset']
    dpi_r = dpi['dpi_r'][:,:2] @ cals['right_gains'] + cals['right_offset']

    for iT, ind in enumerate(eye_trials):
        ind = int(ind)
        if data['MAT'][iT,-1] < 1:
            continue
        dpi_l_trials.append(dpi_l[ind-n_pre:ind+n_post])
        dpi_r_trials.append(dpi_r[ind-n_pre:ind+n_post])

dpi_l_trials = np.stack(dpi_l_trials, axis=0)
dpi_r_trials = np.stack(dpi_r_trials, axis=0)
n_trials = len(dpi_l_trials)

ALLMAT = allmat_data['ALLMAT']

unattended_trials = np.where(ALLMAT[:, 3] == 2)[0]
attended_trials = np.where(ALLMAT[:, 3] == 1)[0]
print(dpi_l_trials.shape)
print(ALLMAT.shape)

#%%
# filter bad trials

if monkey == 'monkeyN':
    attended_y_lim = [0, 400]
    unattended_y_lim = [250, 450]
    attended_x_lim = [-200, 200]
    unattended_x_lim = [-200, 0]
else:
    attended_y_lim = [-np.inf, np.inf]
    unattended_y_lim = [-np.inf, np.inf]
    attended_x_lim = [-np.inf, np.inf]
    unattended_x_lim = [-np.inf, np.inf]
max_skipped_frames = 5
dpi_sampling_rate = 1/500
max_ifi = max_skipped_frames * dpi_sampling_rate

valid_mask = np.ones(dpi_r_trials.shape[0], dtype=bool)
for iT in range(n_trials):
    if iT in attended_trials:
        if np.any((dpi_r_trials[iT,:,1] < attended_y_lim[0]) | (dpi_r_trials[iT,:,1] > attended_y_lim[1])):
            valid_mask[iT] = False
        if np.any((dpi_r_trials[iT,:,0] < attended_x_lim[0]) | (dpi_r_trials[iT,:,0] > attended_x_lim[1])):
            valid_mask[iT] = False
    elif iT in unattended_trials:
        if np.any((dpi_r_trials[iT,:,1] < unattended_y_lim[0]) | (dpi_r_trials[iT,:,1] > unattended_y_lim[1])):
            valid_mask[iT] = False
        if np.any((dpi_r_trials[iT,:,0] < unattended_x_lim[0]) | (dpi_r_trials[iT,:,0] > unattended_x_lim[1])):
            valid_mask[iT] = False
    else:
        print('Error: trial not attended or unattended')

    trial = dpi_r_trials[iT]
    trial_acc = np.linalg.norm(np.diff(np.diff(trial, axis=0, prepend=0), axis=0, prepend=0), axis=1)
    acc_changing = np.diff(trial_acc, prepend=-1, append=1) > 1e-6
    frame_inds = np.where(acc_changing)[0]
    ifi = np.diff(frame_inds) / 30000

    if np.any(ifi > max_ifi):
        valid_mask[iT] = False
        print(f'Trial {iT} has maximum interframe interval of {np.max(ifi)*1000:.3f} ms')


dpi_r_trials = dpi_r_trials[valid_mask]
dpi_l_trials = dpi_l_trials[valid_mask]
ALLMAT = ALLMAT[valid_mask]
n_trials = len(dpi_r_trials)
unattended_trials = np.where(ALLMAT[:, 3] == 2)[0]
attended_trials = np.where(ALLMAT[:, 3] == 1)[0]
print(n_trials)

normMUA = normMUA[:,valid_mask]


# Set up task-specific parameters
control_var_idx = 7  # 8 in MATLAB (1-based) -> 7 in Python (0-based)

# Get unique control values
controls = np.unique(ALLMAT[:, control_var_idx])

# Define a boolean mask for channels that meet the SNR threshold
snr_mask = (norm_mua_data['SNR'].flatten() > snr_threshold) & all_chns

# --- Plot 1: Average activity for attended vs. unattended ---
fig1, ax1 = plt.subplots(figsize=(8, 6))

# Condition for unattended trials (ALLMAT[:, 4] == 2)
unattended_mask = ALLMAT[:, 3] == 2 # Index 3 for 4th column
unattended_activity = np.nanmean(normMUA[np.ix_(snr_mask, unattended_mask)], axis=(0, 1))

# Condition for attended trials (ALLMAT[:, 4] == 1)
attended_mask = ALLMAT[:, 3] == 1 # Index 3 for 4th column
attended_activity = np.nanmean(normMUA[np.ix_(snr_mask, attended_mask)], axis=(0, 1))

ax1.plot(smooth(unattended_activity, 20), color=[.7, .6, .8], linewidth=2, label='Unattended')
ax1.plot(smooth(attended_activity, 20), color=[.3, .7, .2], linewidth=2, label='Attended')

# --- Formatting for Plot 1 ---
ax1.set_xlim(0, 700)
ax1.set_ylim(-0.3, 1.1)
ax1.axvline(x=200, color=[0.5, 0.5, 0.5], linestyle='--', linewidth=1)

tick_indices = np.arange(0, len(tb), 100)
ax1.set_xticks(tick_indices)
ax1.set_xticklabels(np.round(tb[tick_indices], 2))
ax1.set_yticks([0, 1])

ax1.set_ylabel('Normalized activity')
ax1.set_xlabel('Time (ms)')
ax1.spines['top'].set_visible(False)
ax1.spines['right'].set_visible(False)
ax1.legend()
ax1.set_title(f'{monkey.capitalize()} - {task.capitalize()} Task: Overall Activity')
fig1.tight_layout()

#%%

t_trial = np.arange(-n_pre, n_post)/30

n_trials = 100
plt.figure()
plt.subplot(221)
plt.plot(t_trial, dpi_r_trials[unattended_trials[:n_trials],:,0].T, alpha = .6, c='k')
plt.axvline(x=0, color='k', linestyle='--')
plt.title('Unattended X')
plt.subplot(223)
plt.plot(t_trial, dpi_r_trials[attended_trials[:n_trials],:,0].T, alpha = .6, c='r')
plt.axvline(x=0, color='k', linestyle='--')
plt.title('Attended X')
plt.subplot(222)
plt.plot(t_trial, dpi_r_trials[unattended_trials[:n_trials],:,1].T, alpha = .6, c='k')
plt.axvline(x=0, color='k', linestyle='--')
plt.title('Unattended Y')
plt.subplot(224)
plt.plot(t_trial, dpi_r_trials[attended_trials[:n_trials],:,1].T, alpha = .6, c='r')
plt.axvline(x=0, color='k', linestyle='--')
plt.title('Attended Y')
plt.show()

#%%
if 'trial_ind' not in locals():
    trial_ind = 0
else:
    trial_ind += 1

plt.figure()
plt.subplot(311)
plt.plot(t_trial, dpi_r_trials[trial_ind,:,0], c='k')
plt.title(f'Trial {trial_ind} - {"Attended" if (trial_ind in attended_trials) else "Unattended"} X')
plt.subplot(312)
plt.plot(t_trial, dpi_r_trials[trial_ind,:,1], c='k')
plt.title('Y')
plt.subplot(313)
plt.plot(np.arange(-200, 500), np.nanmean(normMUA[snr_mask, trial_ind, :], axis=0))
plt.title('MUA')
plt.tight_layout()
plt.show()

#%%
plt.figure(figsize=(6, 12))
plt.subplot(211)
for i in range(40):
    iT = attended_trials[i]
    plt.plot(t_trial, dpi_r_trials[iT,:,1]+i*20, alpha = 1)
plt.axvline(x=0, color='k', linestyle='--', label='Stimulus Onset')
plt.legend()
plt.title(f'40 Attended Trials - Vertical Eye Position')
plt.subplot(212)
for i in range(40):
    iT = unattended_trials[i]
    plt.plot(t_trial, dpi_r_trials[iT,:,1]+i*20, alpha = 1)
plt.axvline(x=0, color='k', linestyle='--', label='Stimulus Onset')
plt.legend()
plt.title(f'40 Unattended Trials - Vertical Eye Position')
plt.show()


#%%

mean_attended_eyepos = np.mean(dpi_r_trials[attended_trials,:], axis=0)
ste_attended_eyepos = np.std(dpi_r_trials[attended_trials,:], axis=0) / np.sqrt(dpi_r_trials[attended_trials,:].shape[0])
mean_unattended_eyepos = np.mean(dpi_r_trials[unattended_trials,:], axis=0)
ste_unattended_eyepos = np.std(dpi_r_trials[unattended_trials,:], axis=0) / np.sqrt(dpi_r_trials[unattended_trials,:].shape[0])

plt.figure(figsize=(8, 12))
plt.subplot(211)
plt.fill_between(t_trial, 
                 mean_unattended_eyepos[:,0] - ste_unattended_eyepos[:,0]*1.96, 
                 mean_unattended_eyepos[:,0] + ste_unattended_eyepos[:,0]*1.96,
                 color='k', alpha=0.2, label='Unattended - 95% CI')
plt.plot(t_trial, mean_unattended_eyepos[:,0], c='k', label='Unattended - Mean')
plt.fill_between(t_trial, 
                 mean_attended_eyepos[:,0] - ste_attended_eyepos[:,0]*1.96, 
                 mean_attended_eyepos[:,0] + ste_attended_eyepos[:,0]*1.96,
                 color='r', alpha=0.2, label='Attended - 95% CI')
plt.plot(t_trial, mean_attended_eyepos[:,0], c='r', label='Attended - Mean')
plt.legend()
plt.title(f'{monkey.capitalize()} - {task.capitalize()} Task: Eye Position\nX')
plt.axvline(x=0, color='k', linestyle='--')
plt.subplot(212)
plt.fill_between(t_trial, 
                 mean_unattended_eyepos[:,1] - ste_unattended_eyepos[:,1]*1.96, 
                 mean_unattended_eyepos[:,1] + ste_unattended_eyepos[:,1]*1.96,
                 color='k', alpha=0.2, label='Unattended - 95% CI')
plt.fill_between(t_trial, 
                 mean_attended_eyepos[:,1] - ste_attended_eyepos[:,1]*1.96, 
                 mean_attended_eyepos[:,1] + ste_attended_eyepos[:,1]*1.96,
                 color='r', alpha=0.2, label='Attended - 95% CI')
plt.plot(t_trial, mean_unattended_eyepos[:,1], c='k', label='Unattended - Mean')
plt.plot(t_trial, mean_attended_eyepos[:,1], c='r', label='Attended - Mean')
plt.axvline(x=0, color='k', linestyle='--')
plt.legend()
plt.title('Y')
plt.tight_layout()
plt.show()

#%%

attended_speed = np.linalg.norm(np.diff(dpi_r_trials[attended_trials,:], axis=1), axis=2)
unattended_speed = np.linalg.norm(np.diff(dpi_r_trials[unattended_trials,:], axis=1), axis=2)

plt.figure()
plt.plot(t_trial[:-1], attended_speed[:n_trials,:].T, c='r', alpha=.5)
plt.plot(t_trial[:-1], unattended_speed[:n_trials,:].T, c='k', alpha=.5)
plt.show()
#%%
med_attended_speed = np.mean(attended_speed, axis=0)
med_unattended_speed = np.mean(unattended_speed, axis=0)

plt.figure()
plt.plot(t_trial[:-1], med_unattended_speed, c='k')
plt.plot(t_trial[:-1], med_attended_speed, c='r')
plt.show()


# %%
# marginalize on position

window = [200, 400]
n_bins = 4
mask = (window[0] < t_trial) & (t_trial < window[1])
dpi_r_y_win = np.mean(dpi_r_trials[:,mask,1], axis=1)
edges = np.percentile(dpi_r_y_win, np.linspace(0, 100, n_bins + 1))

plt.figure(figsize=(8, 12))
plt.subplot(211)
plt.plot(t_trial, mean_attended_eyepos[:,1], c='r')
plt.plot(t_trial, mean_unattended_eyepos[:,1], c='k')
plt.axvline(x=window[1], color='b', linestyle='--', label='Window')
plt.axvline(x=window[0], color='b', linestyle='--')
plt.title(f'Mean position window - {window[0]} - {window[1]} ms')
plt.legend()
plt.subplot(212)
plt.hist(dpi_r_y_win, bins=50, alpha=0.5, label='All')
plt.hist(dpi_r_y_win[attended_trials], bins=50, alpha=0.5, label='Attended')
plt.hist(dpi_r_y_win[unattended_trials], bins=50, alpha=0.5, label='Unattended')
for e in edges:
    plt.axvline(x=e, color='k', linestyle='--', label='Bin edges' if e == edges[0] else None)
plt.legend()
plt.title('Position histogram - binned')
plt.tight_layout()
plt.show()

#%%

plt.figure()
for i in range(n_bins):
    e0, e1 = edges[i], edges[i+1]
    mask = (e0 < dpi_r_y_win) & (dpi_r_y_win < e1)
    activity = np.nanmean(normMUA[np.ix_(snr_mask, mask)], axis=(0,1))
    plt.plot(activity, label=f'{e0:.1f} - {e1:.1f}')
plt.legend()
plt.title('MUA conditioned on position')
plt.show()

#%%
mua_signal = np.nanmean(normMUA[snr_mask], axis=0)
eyepos_sorted = np.argsort(dpi_r_y_win)

plt.figure()
plt.imshow(mua_signal[eyepos_sorted], vmin=-2, vmax=2, cmap='coolwarm')
plt.colorbar()
plt.title('MUA sorted by vertical eye position')
plt.show()


# %%

attn_activity = np.nanmean(normMUA[np.ix_(snr_mask, attended_mask)], axis=(0,1))
unattn_activity = np.nanmean(normMUA[np.ix_(snr_mask, unattended_mask)], axis=(0,1))

plt.figure(figsize=(8, 12))
for i in range(n_bins):
    e0, e1 = edges[i], edges[i+1]
    mask = (e0 < dpi_r_y_win) & (dpi_r_y_win < e1)
    attn_activity_ctrl = np.nanmean(normMUA[np.ix_(snr_mask, mask & attended_mask)], axis=(0,1))
    unattn_activity_ctrl = np.nanmean(normMUA[np.ix_(snr_mask, mask & unattended_mask)], axis=(0,1))
    plt.subplot(n_bins, 1, i+1)
    plt.plot(tb, attn_activity, c='k')
    plt.plot(tb, unattn_activity, c='k')
    plt.plot(tb, attn_activity_ctrl, label='Attended')
    plt.plot(tb, unattn_activity_ctrl, label='Unattended')
    plt.axvline(x=0, color='k', linestyle='--')
    plt.ylim(-.3, 1.3)
    plt.axhline(y=0, color='k', linestyle='--', zorder=0)
    plt.title(f'{e0:.1f} - {e1:.1f}')
    plt.legend()
plt.tight_layout()
plt.show()


# %%


